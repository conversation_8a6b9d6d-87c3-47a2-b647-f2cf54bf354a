#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取示例文章信息的脚本
专门用于提取你提到的那篇关于杀菌剂检测的文章
"""

from DrissionPage import ChromiumPage
import time
from urllib.parse import urljoin


def extract_specific_article(page):
    """
    提取特定文章的信息
    """
    print("=== 开始提取文章信息 ===")
    
    # 查找所有 .articleEntry 元素
    article_elements = page.eles('.articleEntry')
    print(f"找到 {len(article_elements)} 个 .articleEntry 元素")
    
    for i, article in enumerate(article_elements):
        print(f"\n--- 处理第 {i+1} 个文章 ---")
        
        try:
            # 查找内部的 .tocArticleEntry
            toc_article = article.ele('.tocArticleEntry.include-metrics-panel.toc-article-tools', timeout=2)
            if not toc_article:
                print("未找到 .tocArticleEntry 元素")
                continue
            
            # 提取标题
            title_element = toc_article.ele('.art_title.linkable a .hlFld-Title', timeout=2)
            if title_element:
                title = title_element.text.strip()
                print(f"标题: {title}")
                
                # 检查是否是目标文章
                if "post-harvest fungicides" in title.lower():
                    print("✓ 找到目标文章!")
                    
                    # 提取链接
                    link_element = toc_article.ele('.art_title.linkable a', timeout=2)
                    if link_element:
                        relative_link = link_element.attr('href')
                        full_link = urljoin('https://www.tandfonline.com', relative_link)
                        print(f"链接: {full_link}")
                    
                    # 提取发布日期
                    date_element = toc_article.ele('.tocEPubDate .date', timeout=2)
                    if date_element:
                        publish_date = date_element.text.strip()
                        print(f"日期: {publish_date}")
                    
                    # 提取作者
                    author_elements = toc_article.eles('.tocAuthors .entryAuthor', timeout=2)
                    authors = []
                    for author_elem in author_elements:
                        if author_elem:
                            authors.append(author_elem.text.strip())
                    print(f"作者: {', '.join(authors)}")
                    
                    # 提取DOI
                    checkbox_element = toc_article.ele('.checkbox--input', timeout=2)
                    if checkbox_element:
                        doi = checkbox_element.attr('name') or ""
                        print(f"DOI: {doi}")
                    
                    # 提取文章类型
                    article_type_element = toc_article.ele('.article-type', timeout=2)
                    if article_type_element:
                        article_type = article_type_element.text.strip()
                        print(f"文章类型: {article_type}")
                    
                    # 提取metrics信息
                    metrics_panel = toc_article.ele('.metrics-panel', timeout=2)
                    if metrics_panel:
                        # Views
                        views_elem = metrics_panel.ele('li:contains("Views")', timeout=1)
                        if views_elem:
                            views_span = views_elem.ele('span', timeout=1)
                            if views_span:
                                views = views_span.text.strip()
                                print(f"浏览量: {views}")
                        
                        # Citations
                        citations_elem = metrics_panel.ele('li:contains("CrossRef citations")', timeout=1)
                        if citations_elem:
                            citations_span = citations_elem.ele('span', timeout=1)
                            if citations_span:
                                citations = citations_span.text.strip()
                                print(f"引用数: {citations}")
                        
                        # Altmetric
                        altmetric_elem = metrics_panel.ele('.metrics-score', timeout=1)
                        if altmetric_elem:
                            altmetric_score = altmetric_elem.text.strip()
                            print(f"Altmetric分数: {altmetric_score}")
                    
                    print("\n=== 提取完成 ===")
                    return {
                        'title': title,
                        'publish_date': publish_date,
                        'link': full_link,
                        'authors': authors,
                        'doi': doi,
                        'article_type': article_type
                    }
            else:
                print("未找到标题元素")
                
        except Exception as e:
            print(f"处理文章时出错: {e}")
            continue
    
    print("未找到目标文章")
    return None


def main():
    """
    主函数
    """
    url = "https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20"
    
    # 创建页面对象
    page = ChromiumPage()
    
    try:
        print(f"正在访问: {url}")
        page.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)
        
        print(f"页面标题: {page.title}")
        
        # 滚动页面确保内容加载
        page.scroll.to_bottom()
        time.sleep(2)
        
        # 提取特定文章
        article_info = extract_specific_article(page)
        
        if article_info:
            print("\n=== 最终结果 ===")
            print(f"标题: {article_info['title']}")
            print(f"日期: {article_info['publish_date']}")
            print(f"链接: {article_info['link']}")
            print(f"DOI: {article_info['doi']}")
            print(f"作者: {', '.join(article_info['authors'])}")
        else:
            print("未能提取到目标文章信息")
        
    except Exception as e:
        print(f"抓取过程中出错: {e}")
        
    finally:
        # 关闭浏览器
        page.quit()


if __name__ == "__main__":
    main()
